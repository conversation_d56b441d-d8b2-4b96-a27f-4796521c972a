/**
 * 关卡编辑器 - SplitSecond Spark
 * 可视化关卡编辑工具
 */

class LevelEditor {
    constructor() {
        this.levelManager = window.levelManager;
        this.currentLevel = null;
        this.isEditing = false;
        this.selectedTool = 'platform';
        this.selectedDimension = 'present';
        this.gridSize = 20;
        this.showGrid = true;
        
        // 编辑器状态
        this.editorState = {
            zoom: 1.0,
            offsetX: 0,
            offsetY: 0,
            isDragging: false,
            dragStart: { x: 0, y: 0 },
            selectedObject: null,
            clipboard: null
        };

        // 工具类型
        this.tools = {
            SELECT: 'select',
            PLATFORM: 'platform',
            SPARK: 'spark',
            OBSTACLE: 'obstacle',
            PORTAL: 'portal',
            TRIGGER: 'trigger',
            PLAYER_START: 'playerStart',
            ERASER: 'eraser'
        };

        // 对象类型配置
        this.objectTypes = {
            platform: {
                name: '平台',
                color: '#8B4513',
                defaultSize: { width: 100, height: 20 }
            },
            spark: {
                name: '能量火花',
                color: '#FFD700',
                defaultSize: { width: 16, height: 16 }
            },
            obstacle: {
                name: '障碍物',
                color: '#FF4500',
                defaultSize: { width: 40, height: 40 }
            },
            portal: {
                name: '传送门',
                color: '#9932CC',
                defaultSize: { width: 60, height: 80 }
            },
            trigger: {
                name: '触发器',
                color: '#00CED1',
                defaultSize: { width: 50, height: 50 }
            }
        };

        this.initializeEditor();
        
        DebugUtils.log('关卡编辑器初始化完成');
    }

    /**
     * 初始化编辑器
     */
    initializeEditor() {
        this.setupCanvas();
        this.setupEventListeners();
    }

    /**
     * 设置画布
     */
    setupCanvas() {
        this.canvas = DOMUtils.$('#level-editor-canvas');
        if (!this.canvas) {
            DebugUtils.log('找不到关卡编辑器画布', 'error');
            return;
        }

        this.ctx = this.canvas.getContext('2d');
        this.resizeCanvas();
        
        // 设置画布样式
        this.canvas.style.cursor = 'crosshair';
        this.canvas.style.border = '2px solid #333';
        this.canvas.style.background = '#1a1a1a';
    }

    /**
     * 调整画布大小
     */
    resizeCanvas() {
        if (!this.canvas) return;
        
        const container = this.canvas.parentElement;
        if (container) {
            this.canvas.width = container.clientWidth;
            this.canvas.height = container.clientHeight;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        if (!this.canvas) return;

        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('wheel', (e) => this.handleWheel(e));

        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));

        // 窗口大小变化
        window.addEventListener('resize', () => this.resizeCanvas());
    }

    /**
     * 创建新关卡
     * @param {string} name - 关卡名称
     * @param {string} description - 关卡描述
     */
    async createNewLevel(name, description = '') {
        try {
            const level = await this.levelManager.createLevel(name, description);
            if (level) {
                this.currentLevel = level;
                this.isEditing = true;
                this.resetEditorState();
                this.render();
                DebugUtils.log(`开始编辑新关卡: ${name}`);
                return true;
            }
            return false;
        } catch (error) {
            DebugUtils.log(`创建新关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 加载关卡进行编辑
     * @param {string} levelId - 关卡ID
     */
    async loadLevel(levelId) {
        try {
            const level = await this.levelManager.loadLevel(levelId);
            if (level) {
                this.currentLevel = level;
                this.isEditing = true;
                this.resetEditorState();
                this.render();
                DebugUtils.log(`开始编辑关卡: ${level.name}`);
                return true;
            }
            return false;
        } catch (error) {
            DebugUtils.log(`加载关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 保存当前关卡
     */
    async saveLevel() {
        if (!this.currentLevel) {
            DebugUtils.log('没有当前关卡可保存', 'error');
            return false;
        }

        try {
            const success = await this.levelManager.saveLevel(this.currentLevel);
            if (success) {
                DebugUtils.log(`关卡保存成功: ${this.currentLevel.name}`);
            }
            return success;
        } catch (error) {
            DebugUtils.log(`保存关卡失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 重置编辑器状态
     */
    resetEditorState() {
        this.editorState = {
            zoom: 1.0,
            offsetX: 0,
            offsetY: 0,
            isDragging: false,
            dragStart: { x: 0, y: 0 },
            selectedObject: null,
            clipboard: null
        };
    }

    /**
     * 设置当前工具
     * @param {string} tool - 工具类型
     */
    setTool(tool) {
        if (this.tools[tool.toUpperCase()]) {
            this.selectedTool = tool;
            this.updateCursor();
            DebugUtils.log(`切换到工具: ${tool}`);
        }
    }

    /**
     * 设置当前时间维度
     * @param {string} dimension - 时间维度
     */
    setDimension(dimension) {
        if (['past', 'present', 'future'].includes(dimension)) {
            this.selectedDimension = dimension;
            this.render();
            DebugUtils.log(`切换到时间维度: ${dimension}`);
        }
    }

    /**
     * 更新鼠标光标
     */
    updateCursor() {
        if (!this.canvas) return;
        
        switch (this.selectedTool) {
            case this.tools.SELECT:
                this.canvas.style.cursor = 'default';
                break;
            case this.tools.ERASER:
                this.canvas.style.cursor = 'crosshair';
                break;
            default:
                this.canvas.style.cursor = 'crosshair';
                break;
        }
    }

    /**
     * 处理鼠标按下事件
     */
    handleMouseDown(e) {
        if (!this.isEditing) return;

        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // 转换为世界坐标
        const worldPos = this.screenToWorld(x, y);
        
        this.editorState.isDragging = true;
        this.editorState.dragStart = { x: worldPos.x, y: worldPos.y };

        switch (this.selectedTool) {
            case this.tools.SELECT:
                this.handleSelectTool(worldPos);
                break;
            case this.tools.PLATFORM:
                this.handlePlatformTool(worldPos);
                break;
            case this.tools.SPARK:
                this.handleSparkTool(worldPos);
                break;
            case this.tools.OBSTACLE:
                this.handleObstacleTool(worldPos);
                break;
            case this.tools.PORTAL:
                this.handlePortalTool(worldPos);
                break;
            case this.tools.TRIGGER:
                this.handleTriggerTool(worldPos);
                break;
            case this.tools.PLAYER_START:
                this.handlePlayerStartTool(worldPos);
                break;
            case this.tools.ERASER:
                this.handleEraserTool(worldPos);
                break;
        }

        this.render();
    }

    /**
     * 处理鼠标移动事件
     */
    handleMouseMove(e) {
        if (!this.isEditing) return;

        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const worldPos = this.screenToWorld(x, y);

        if (this.editorState.isDragging) {
            // 处理拖拽操作
            if (this.selectedTool === this.tools.SELECT && this.editorState.selectedObject) {
                this.moveSelectedObject(worldPos);
                this.render();
            }
        }
    }

    /**
     * 处理鼠标释放事件
     */
    handleMouseUp(e) {
        this.editorState.isDragging = false;
    }

    /**
     * 处理滚轮事件（缩放）
     */
    handleWheel(e) {
        e.preventDefault();
        
        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        const newZoom = Math.max(0.1, Math.min(5.0, this.editorState.zoom * zoomFactor));
        
        if (newZoom !== this.editorState.zoom) {
            this.editorState.zoom = newZoom;
            this.render();
        }
    }

    /**
     * 处理键盘按下事件
     */
    handleKeyDown(e) {
        if (!this.isEditing) return;

        switch (e.key) {
            case 'Delete':
                this.deleteSelectedObject();
                break;
            case 'c':
                if (e.ctrlKey) {
                    this.copySelectedObject();
                }
                break;
            case 'v':
                if (e.ctrlKey) {
                    this.pasteObject();
                }
                break;
            case 's':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.saveLevel();
                }
                break;
            case 'g':
                this.showGrid = !this.showGrid;
                this.render();
                break;
        }
    }

    /**
     * 处理键盘释放事件
     */
    handleKeyUp(e) {
        // 预留给需要的键盘释放处理
    }

    /**
     * 屏幕坐标转世界坐标
     */
    screenToWorld(screenX, screenY) {
        return {
            x: (screenX - this.editorState.offsetX) / this.editorState.zoom,
            y: (screenY - this.editorState.offsetY) / this.editorState.zoom
        };
    }

    /**
     * 世界坐标转屏幕坐标
     */
    worldToScreen(worldX, worldY) {
        return {
            x: worldX * this.editorState.zoom + this.editorState.offsetX,
            y: worldY * this.editorState.zoom + this.editorState.offsetY
        };
    }

    /**
     * 对齐到网格
     */
    snapToGrid(x, y) {
        if (!this.showGrid) return { x, y };
        
        return {
            x: Math.round(x / this.gridSize) * this.gridSize,
            y: Math.round(y / this.gridSize) * this.gridSize
        };
    }
}

// 创建全局关卡编辑器实例
window.levelEditor = new LevelEditor();

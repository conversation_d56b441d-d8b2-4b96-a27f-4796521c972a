/* UI界面样式 - SplitSecond Spark */

/* 暂停菜单 */
.pause-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 200;
    backdrop-filter: blur(5px);
}

.pause-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 400px;
    width: 90%;
}

.pause-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #ffffff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.pause-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 设置菜单 */
.settings-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 300;
    backdrop-filter: blur(5px);
}

.settings-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    width: 90%;
}

.settings-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.settings-options {
    margin-bottom: 30px;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item label {
    font-size: 1rem;
    color: #ffffff;
    font-weight: 600;
    min-width: 100px;
}

.setting-item input[type="range"] {
    flex: 1;
    margin: 0 15px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
}

.setting-item input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: linear-gradient(45deg, #00d4ff, #4ecdc4);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
}

.setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    padding: 8px 12px;
    color: #ffffff;
    font-size: 1rem;
    outline: none;
    cursor: pointer;
}

.setting-item select option {
    background: #1a1a2e;
    color: #ffffff;
}

.volume-value {
    font-size: 0.9rem;
    color: #4ecdc4;
    font-weight: 600;
    min-width: 40px;
    text-align: right;
}

.settings-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* 帮助菜单 */
.help-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 300;
    backdrop-filter: blur(5px);
    overflow-y: auto;
}

.help-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    margin: 20px;
}

.help-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.help-sections {
    margin-bottom: 30px;
}

.help-section {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.help-section h3 {
    font-size: 1.3rem;
    color: #4ecdc4;
    margin-bottom: 15px;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.help-section p {
    font-size: 1rem;
    color: #e0e0e0;
    line-height: 1.6;
    margin-bottom: 10px;
}

.help-section ul {
    list-style: none;
    padding-left: 0;
}

.help-section li {
    font-size: 1rem;
    color: #e0e0e0;
    line-height: 1.6;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.help-section li::before {
    content: "▶";
    position: absolute;
    left: 0;
    color: #4ecdc4;
    font-size: 0.8rem;
}

.help-section strong {
    color: #00d4ff;
    font-weight: 600;
}

/* 通知系统 */
.notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    font-size: 1rem;
    z-index: 400;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid #4ecdc4;
}

.notification.warning {
    border-left: 4px solid #f39c12;
}

.notification.error {
    border-left: 4px solid #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .pause-content,
    .settings-content,
    .help-content {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .setting-item label {
        min-width: auto;
    }
    
    .setting-item input[type="range"] {
        width: 100%;
        margin: 0;
    }
    
    .settings-buttons {
        flex-direction: column;
    }
    
    .help-content {
        max-height: 90vh;
    }
}

@media (max-width: 480px) {
    .pause-content h2,
    .settings-content h2,
    .help-content h2 {
        font-size: 1.5rem;
    }
    
    .help-section {
        padding: 15px;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .notification.show {
        transform: translateY(0);
    }
}

/* 账号管理界面样式 */
.current-account-info {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.account-display {
    display: flex;
    align-items: center;
    gap: 15px;
}

.account-avatar {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.account-name {
    flex: 1;
    font-size: 1.2rem;
    font-weight: bold;
    color: #ffffff;
}

.account-switch-btn {
    padding: 8px 16px;
    background: rgba(0, 212, 255, 0.3);
    border: 1px solid rgba(0, 212, 255, 0.5);
    border-radius: 20px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.account-switch-btn:hover {
    background: rgba(0, 212, 255, 0.5);
    transform: translateY(-2px);
}

.account-manager-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 200;
    backdrop-filter: blur(5px);
}

.account-manager-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.account-manager-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.account-sections {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.account-sections h3 {
    font-size: 1.3rem;
    color: #ffffff;
    margin-bottom: 15px;
    border-bottom: 2px solid rgba(0, 212, 255, 0.5);
    padding-bottom: 5px;
}

.account-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.account-card.current {
    border-color: rgba(0, 212, 255, 0.5);
    background: rgba(0, 212, 255, 0.1);
}

.account-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.account-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 15px;
}

.account-avatar-large {
    font-size: 3rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.account-details {
    flex: 1;
}

.account-details .account-name {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 10px;
}

.account-stats {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.account-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.action-btn {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.accounts-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.account-list-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

/* 对话框样式 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 300;
    backdrop-filter: blur(5px);
}

.dialog-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    width: 90%;
}

.dialog-content h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #ffffff;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    color: #ffffff;
    font-size: 1rem;
    box-sizing: border-box;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.avatar-selector {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.avatar-option {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-option:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.avatar-option.selected {
    border-color: rgba(0, 212, 255, 0.8);
    background: rgba(0, 212, 255, 0.2);
}

.dialog-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

/* 排行榜界面样式 */
.leaderboard-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 200;
    backdrop-filter: blur(5px);
}

.leaderboard-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.leaderboard-content h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.leaderboard-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.tab-btn.active {
    background: rgba(0, 212, 255, 0.3);
    border-color: rgba(0, 212, 255, 0.5);
}

.leaderboard-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.current-rank-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.rank-display,
.best-score-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.rank-label,
.score-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.rank-value,
.score-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffffff;
}

.leaderboard-list-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.leaderboard-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.leaderboard-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.leaderboard-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.leaderboard-item.current-user {
    border-color: rgba(0, 212, 255, 0.5);
    background: rgba(0, 212, 255, 0.1);
}

.rank-number {
    font-size: 1.2rem;
    font-weight: bold;
    color: #ffffff;
    min-width: 40px;
    text-align: center;
}

.rank-number.top-1 {
    color: #ffd700;
}

.rank-number.top-2 {
    color: #c0c0c0;
}

.rank-number.top-3 {
    color: #cd7f32;
}

.player-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.player-avatar {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.player-details {
    flex: 1;
}

.player-name {
    font-size: 1rem;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 2px;
}

.player-stats {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.score-info {
    text-align: right;
}

.score-value-large {
    font-size: 1.3rem;
    font-weight: bold;
    color: #ffffff;
}

.score-details {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 2px;
}

.leaderboard-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.empty-leaderboard {
    text-align: center;
    padding: 40px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1.1rem;
}

/* 账号管理响应式设计 */
@media (max-width: 768px) {
    .account-display {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .account-info {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .account-actions {
        justify-content: center;
    }

    .account-list-actions {
        flex-direction: column;
        align-items: center;
    }

    .dialog-actions {
        flex-direction: column;
    }

    .account-manager-content,
    .leaderboard-content {
        padding: 20px;
        max-width: 95%;
    }

    .current-rank-info {
        flex-direction: column;
        gap: 15px;
    }

    .leaderboard-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .player-info {
        flex-direction: column;
        text-align: center;
    }

    .leaderboard-tabs {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 100%;
        max-width: 200px;
    }
}

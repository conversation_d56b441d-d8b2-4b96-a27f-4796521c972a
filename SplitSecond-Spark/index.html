<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SplitSecond Spark - 时空火花</title>
    <meta name="description" content="一款创新的时空探索解谜游戏">
    <meta name="keywords" content="游戏,时空,探索,解谜,web游戏">
    
    <!-- PWA 支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1a1a2e">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="apple-touch-icon" href="assets/icon-192.png">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/game.css">
    <link rel="stylesheet" href="css/ui.css">
    <link rel="stylesheet" href="css/effects.css">
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="game-logo">
                <h1>SplitSecond Spark</h1>
                <p>时空火花</p>
            </div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-text">正在加载游戏资源...</div>
        </div>
    </div>

    <!-- 主菜单 -->
    <div id="main-menu" class="main-menu hidden">
        <div class="menu-background"></div>
        <div class="menu-content">
            <div class="game-title">
                <h1>SplitSecond Spark</h1>
                <p class="subtitle">时空火花探索之旅</p>
            </div>
            <div class="current-account-info">
                <div class="account-display">
                    <span class="account-avatar">👤</span>
                    <span id="current-account-name" class="account-name">默认玩家</span>
                    <button id="switch-account-btn" class="account-switch-btn">切换账号</button>
                </div>
            </div>
            <div class="menu-buttons">
                <button id="new-game-btn" class="menu-btn primary">开始新游戏</button>
                <button id="continue-game-btn" class="menu-btn secondary">继续游戏</button>
                <button id="account-manager-btn" class="menu-btn secondary">账号管理</button>
                <button id="leaderboard-btn" class="menu-btn secondary">排行榜</button>
                <button id="level-editor-btn" class="menu-btn secondary">关卡编辑器</button>
                <button id="settings-btn" class="menu-btn secondary">设置</button>
                <button id="help-btn" class="menu-btn secondary">帮助</button>
            </div>
        </div>
    </div>

    <!-- 游戏主界面 -->
    <div id="game-container" class="game-container hidden">
        <!-- 游戏画布 -->
        <canvas id="game-canvas" class="game-canvas"></canvas>
        
        <!-- 游戏UI -->
        <div class="game-ui">
            <!-- 顶部状态栏 -->
            <div class="top-bar">
                <div class="time-dimension-indicator">
                    <span class="time-label">时间维度:</span>
                    <div class="time-buttons">
                        <button id="past-btn" class="time-btn past">过去</button>
                        <button id="present-btn" class="time-btn present active">现在</button>
                        <button id="future-btn" class="time-btn future">未来</button>
                    </div>
                </div>
                <div class="spark-counter">
                    <span class="spark-icon">⚡</span>
                    <span id="spark-count">0</span>
                </div>
                <button id="pause-btn" class="pause-btn">⏸️</button>
            </div>

            <!-- 移动端控制器 -->
            <div class="mobile-controls">
                <div class="movement-pad">
                    <div class="movement-center"></div>
                    <button class="move-btn up" data-direction="up">↑</button>
                    <button class="move-btn down" data-direction="down">↓</button>
                    <button class="move-btn left" data-direction="left">←</button>
                    <button class="move-btn right" data-direction="right">→</button>
                </div>
                <div class="action-buttons">
                    <button id="interact-btn" class="action-btn">交互</button>
                    <button id="time-switch-btn" class="action-btn">时空切换</button>
                </div>
            </div>
        </div>

        <!-- 暂停菜单 -->
        <div id="pause-menu" class="pause-menu hidden">
            <div class="pause-content">
                <h2>游戏暂停</h2>
                <div class="pause-buttons">
                    <button id="resume-btn" class="menu-btn primary">继续游戏</button>
                    <button id="save-game-btn" class="menu-btn secondary">保存游戏</button>
                    <button id="main-menu-btn" class="menu-btn secondary">返回主菜单</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置界面 -->
    <div id="settings-menu" class="settings-menu hidden">
        <div class="settings-content">
            <h2>游戏设置</h2>
            <div class="settings-options">
                <div class="setting-item">
                    <label for="music-volume">音乐音量:</label>
                    <input type="range" id="music-volume" min="0" max="100" value="70">
                    <span class="volume-value">70%</span>
                </div>
                <div class="setting-item">
                    <label for="sfx-volume">音效音量:</label>
                    <input type="range" id="sfx-volume" min="0" max="100" value="80">
                    <span class="volume-value">80%</span>
                </div>
                <div class="setting-item">
                    <label for="graphics-quality">画质设置:</label>
                    <select id="graphics-quality">
                        <option value="low">低</option>
                        <option value="medium" selected>中</option>
                        <option value="high">高</option>
                    </select>
                </div>
            </div>
            <div class="settings-buttons">
                <button id="settings-back-btn" class="menu-btn primary">返回</button>
                <button id="reset-settings-btn" class="menu-btn secondary">重置设置</button>
            </div>
        </div>
    </div>

    <!-- 账号管理界面 -->
    <div id="account-manager-menu" class="account-manager-menu hidden">
        <div class="account-manager-content">
            <h2>账号管理</h2>
            <div class="account-sections">
                <div class="current-account-section">
                    <h3>当前账号</h3>
                    <div class="account-card current">
                        <div class="account-info">
                            <span class="account-avatar-large">👤</span>
                            <div class="account-details">
                                <div id="current-account-display-name" class="account-name">默认玩家</div>
                                <div class="account-stats">
                                    <span>总游戏时间: <span id="current-account-playtime">0分钟</span></span>
                                    <span>收集火花: <span id="current-account-sparks">0</span></span>
                                    <span>最高分数: <span id="current-account-score">0</span></span>
                                </div>
                            </div>
                        </div>
                        <div class="account-actions">
                            <button id="edit-account-btn" class="action-btn">编辑</button>
                            <button id="export-account-btn" class="action-btn">导出</button>
                        </div>
                    </div>
                </div>

                <div class="accounts-list-section">
                    <h3>所有账号</h3>
                    <div id="accounts-list" class="accounts-list">
                        <!-- 账号列表将通过JavaScript动态生成 -->
                    </div>
                    <div class="account-list-actions">
                        <button id="create-account-btn" class="menu-btn primary">创建新账号</button>
                        <button id="import-account-btn" class="menu-btn secondary">导入账号</button>
                    </div>
                </div>
            </div>
            <button id="account-manager-back-btn" class="menu-btn primary">返回</button>
        </div>
    </div>

    <!-- 创建账号对话框 -->
    <div id="create-account-dialog" class="dialog-overlay hidden">
        <div class="dialog-content">
            <h3>创建新账号</h3>
            <div class="form-group">
                <label for="new-account-name">账号名称:</label>
                <input type="text" id="new-account-name" placeholder="请输入账号名称" maxlength="20">
            </div>
            <div class="form-group">
                <label for="new-account-avatar">选择头像:</label>
                <div class="avatar-selector">
                    <button class="avatar-option selected" data-avatar="👤">👤</button>
                    <button class="avatar-option" data-avatar="🎮">🎮</button>
                    <button class="avatar-option" data-avatar="⚡">⚡</button>
                    <button class="avatar-option" data-avatar="🌟">🌟</button>
                    <button class="avatar-option" data-avatar="🚀">🚀</button>
                    <button class="avatar-option" data-avatar="🎯">🎯</button>
                </div>
            </div>
            <div class="dialog-actions">
                <button id="confirm-create-account" class="menu-btn primary">创建</button>
                <button id="cancel-create-account" class="menu-btn secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 导入账号对话框 -->
    <div id="import-account-dialog" class="dialog-overlay hidden">
        <div class="dialog-content">
            <h3>导入账号数据</h3>
            <div class="form-group">
                <label for="import-account-data">粘贴账号数据:</label>
                <textarea id="import-account-data" placeholder="请粘贴导出的账号数据JSON..." rows="10"></textarea>
            </div>
            <div class="dialog-actions">
                <button id="confirm-import-account" class="menu-btn primary">导入</button>
                <button id="cancel-import-account" class="menu-btn secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 排行榜界面 -->
    <div id="leaderboard-menu" class="leaderboard-menu hidden">
        <div class="leaderboard-content">
            <h2>排行榜</h2>
            <div class="leaderboard-tabs">
                <button id="global-tab" class="tab-btn active" data-type="global">全球排行</button>
                <button id="daily-tab" class="tab-btn" data-type="daily">今日排行</button>
                <button id="weekly-tab" class="tab-btn" data-type="weekly">本周排行</button>
                <button id="personal-tab" class="tab-btn" data-type="personal">个人记录</button>
            </div>

            <div class="leaderboard-stats">
                <div class="current-rank-info">
                    <div class="rank-display">
                        <span class="rank-label">当前排名:</span>
                        <span id="current-rank" class="rank-value">未上榜</span>
                    </div>
                    <div class="best-score-display">
                        <span class="score-label">最高分数:</span>
                        <span id="best-score" class="score-value">0</span>
                    </div>
                </div>
            </div>

            <div class="leaderboard-list-container">
                <div id="leaderboard-list" class="leaderboard-list">
                    <!-- 排行榜列表将通过JavaScript动态生成 -->
                </div>
            </div>

            <div class="leaderboard-actions">
                <button id="refresh-leaderboard-btn" class="menu-btn secondary">刷新</button>
                <button id="leaderboard-back-btn" class="menu-btn primary">返回</button>
            </div>
        </div>
    </div>

    <!-- 帮助界面 -->
    <div id="help-menu" class="help-menu hidden">
        <div class="help-content">
            <h2>游戏帮助</h2>
            <div class="help-sections">
                <div class="help-section">
                    <h3>游戏目标</h3>
                    <p>收集散布在不同时间维度的能量火花，解开时空谜题，探索神秘的时空世界。</p>
                </div>
                <div class="help-section">
                    <h3>操作方式</h3>
                    <ul>
                        <li><strong>移动:</strong> 使用WASD键或方向键移动角色</li>
                        <li><strong>时间切换:</strong> 点击顶部的时间按钮或按数字键1、2、3</li>
                        <li><strong>交互:</strong> 按空格键与物体交互</li>
                        <li><strong>暂停:</strong> 按ESC键暂停游戏</li>
                    </ul>
                </div>
                <div class="help-section">
                    <h3>游戏机制</h3>
                    <ul>
                        <li><strong>时间维度:</strong> 在过去、现在、未来三个时间线之间切换</li>
                        <li><strong>能量火花:</strong> 不同颜色的火花有不同效果</li>
                        <li><strong>时空谜题:</strong> 某些谜题需要在不同时间线协调解决</li>
                    </ul>
                </div>
            </div>
            <button id="help-back-btn" class="menu-btn primary">返回</button>
        </div>
    </div>

    <!-- 游戏脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/account-manager.js"></script>
    <script src="js/account-ui.js"></script>
    <script src="js/leaderboard.js"></script>
    <script src="js/leaderboard-ui.js"></script>
    <script src="js/input.js"></script>
    <script src="js/renderer.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/entities.js"></script>
    <script src="js/world.js"></script>
    <script src="js/time-dimension.js"></script>
    <script src="js/player.js"></script>
    <script src="js/spark-system.js"></script>
    <script src="js/game.js"></script>
    <script src="js/init.js"></script>
</body>
</html>
